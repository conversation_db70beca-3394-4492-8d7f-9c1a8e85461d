import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  Drawer,
  Form,
  Button,
  Typography,
  Empty
} from 'antd';
import { CloseOutlined, SettingOutlined, BranchesOutlined } from '@ant-design/icons';
import {
  PLCPanelConfigDrawerProps,
  PLCPanelConfiguration
} from './types/PLCTypes';

const { Title } = Typography;

interface PhaseIdentificationConfigDrawerProps {
  open: boolean;
  onClose: () => void;
  panelId: string | null;
  configuration?: PLCPanelConfiguration;
  onConfigurationSave: (panelId: string, config: PLCPanelConfiguration) => void;
}

const PhaseIdentificationConfigDrawer: React.FC<PhaseIdentificationConfigDrawerProps> = ({
  open,
  onClose,
  panelId,
  configuration,
  onConfigurationSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Handle drawer close
  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  // Initialize form with configuration data when drawer opens
  useEffect(() => {
    if (open && configuration) {
      // Set form values when configuration is available
      form.setFieldsValue({
        // Will add form fields here when implementing API integration
      });
    } else if (open && !configuration) {
      // Reset form for new panel
      form.resetFields();
    }
  }, [open, configuration, form]);

  // Handle form submission (placeholder for now)
  const handleSubmit = async (values: any) => {
    if (!panelId) {
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement API calls for phase identification data
      
      // For now, create a basic configuration to mark panel as configured
      const basicConfiguration: PLCPanelConfiguration = {
        dataRange: {
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0],
          startTime: '00:00',
          endTime: '23:59',
        },
        basic: {
          xAxisColumn: 'DateTime',
          selectedColumns: { indices: [], headers: [] },
          group: 'Phase',
        },
        advanced: {
          windowMode: false,
        },
        title: 'Phase Identification Analysis',
        panelType: 'PhaseIdentificationPanel' as any,
        lastModified: new Date().toISOString(),
      };

      onConfigurationSave(panelId, basicConfiguration);
      handleClose();
    } catch (error: any) {
      console.error('Error saving phase identification configuration:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '16px',
            fontWeight: 600
          }}>
            <BranchesOutlined style={{ color: '#52c41a' }} />
            Phase Identification Configuration
          </div>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={handleClose}
            size="small"
            disabled={loading}
          />
        </div>
      }
      placement="right"
      width={450}
      open={open}
      onClose={handleClose}
      closable={false}
      maskClosable={!loading}
      styles={{
        body: { padding: '0', position: 'relative' }
      }}
    >
      <div 
        className="phase-identification-config-drawer" 
        style={{ 
          minHeight: '100vh',
          pointerEvents: loading ? 'none' : 'auto'
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="phase-identification-config-form"
        >
          {/* Configuration Content - Empty for now */}
          <div style={{
            background: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            margin: '16px',
            marginBottom: '20px',
            border: '1px solid #e8e8e8',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
            minHeight: '400px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Empty
              image={<BranchesOutlined style={{ fontSize: '48px', color: '#52c41a' }} />}
              description={
                <div className="text-center">
                  <h3 className="text-base font-medium text-gray-700 mb-2">
                    Configuration Coming Soon
                  </h3>
                  <p className="text-gray-500 mb-4 text-sm">
                    Phase identification configuration options will be available here once API integration is complete.
                  </p>
                </div>
              }
            />
          </div>

          {/* Temporary Submit Button for Basic Flow */}
          <div style={{
            background: '#ffffff',
            borderRadius: '12px',
            padding: '20px',
            margin: '0 16px 20px 16px',
            border: '1px solid #e8e8e8',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
          }}>
            <div style={{
              marginBottom: '20px',
              paddingBottom: '12px',
              borderBottom: '2px solid #52c41a'
            }}>
              <Title level={5} style={{
                margin: 0,
                color: '#52c41a',
                fontSize: '16px',
                fontWeight: 600,
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                🚀 Basic Setup
              </Title>
            </div>
            
            <p style={{
              color: '#666',
              fontSize: '13px',
              marginBottom: '16px'
            }}>
              Click the button below to mark this panel as configured. API integration and actual configuration options will be added later.
            </p>

            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              disabled={loading}
              style={{
                width: '100%',
                height: '40px',
                fontSize: '14px',
                fontWeight: 600,
                borderRadius: '8px',
                background: '#52c41a',
                borderColor: '#52c41a',
                boxShadow: '0 2px 4px rgba(82, 196, 26, 0.2)'
              }}
              size="large"
            >
              {loading ? 'Configuring...' : 'Mark as Configured'}
            </Button>
          </div>
        </Form>
      </div>
    </Drawer>
  );
};

export default PhaseIdentificationConfigDrawer; 
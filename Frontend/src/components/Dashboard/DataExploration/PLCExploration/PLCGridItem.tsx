import React, { useState, useRef } from 'react';
import { Button } from 'antd';
import { 
  ExpandOutlined, 
  SettingOutlined, 
  CloseOutlined 
} from '@ant-design/icons';
import { 
  PLCGridItemProps, 
  PLCComponentType 
} from './types/PLCTypes';
import BatchComparisonPanel from './panels/BatchComparisonPanel';
import PhaseIdentificationPanel from './panels/PhaseIdentificationPanel';
import FullScreenModal from '../FullScreenModal';

const PLCGridItem: React.FC<PLCGridItemProps> = ({
  data,
  layout,
  onRemove,
  onOpenConfiguration,
  onConfigurationChange
}) => {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const panelContentRef = useRef<HTMLDivElement>(null);

  // Handle panel removal
  const handleRemove = () => {
    if (onRemove) {
      onRemove(data.id);
    }
  };

  // Handle opening configuration drawer
  const handleOpenConfiguration = () => {
    if (onOpenConfiguration) {
      // Get currently displayed columns from the panel component
      // For now, we'll pass undefined and let the panel determine the columns
      onOpenConfiguration(data.id, undefined);
    }
  };

  // Handle fullscreen toggle
  const handleFullScreen = () => {
    setIsFullScreen(true);
  };

  // Handle configuration changes from panel
  const handleConfigurationChange = (config: any) => {
    if (onConfigurationChange) {
      onConfigurationChange(data.id, config);
    }
  };

  // Render panel content based on type
  const renderPanelContent = (isFullScreenMode: boolean = false) => {
    switch (data.type) {
      case PLCComponentType.BatchComparisonPanel:
        return (
          <BatchComparisonPanel
            configuration={data.config}
            selectedColumns={data.config?.basic?.selectedColumns || { indices: [], headers: [] }}
            isFullScreen={isFullScreenMode}
            isEmpty={!data.isConfigured}
            onConfigurationChange={handleConfigurationChange}
            onOpenConfiguration={handleOpenConfiguration}
          />
        );
      case PLCComponentType.PhaseIdentificationPanel:
        return (
          <PhaseIdentificationPanel
            configuration={data.config}
            selectedColumns={data.config?.basic?.selectedColumns || { indices: [], headers: [] }}
            isFullScreen={isFullScreenMode}
            isEmpty={!data.isConfigured}
            onConfigurationChange={handleConfigurationChange}
            onOpenConfiguration={handleOpenConfiguration}
          />
        );
      default:
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <p>Unknown panel type: {data.type}</p>
            </div>
          </div>
        );
    }
  };

  return (
    <>
      <div className="plc-grid-item h-full flex flex-col bg-white border border-gray-200 rounded-lg shadow-sm">
        {/* Panel Header */}
        <div className="plc-grid-item-header flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50 rounded-t-lg">
          <div className="plc-drag-handle flex-1 cursor-move">
            <h4 className="text-sm font-medium text-gray-700 m-0">
              {data.title}
            </h4>
          </div>
          
          <div className="plc-grid-item-controls flex items-center gap-1 no-drag">
            {/* Fullscreen Button */}
            <Button
              type="text"
              size="small"
              icon={<ExpandOutlined />}
              onClick={handleFullScreen}
              title="Expand to fullscreen"
              className="hover:bg-gray-200"
            />
            
            {/* Settings Button */}
            <Button
              type="text"
              size="small"
              icon={<SettingOutlined />}
              onClick={handleOpenConfiguration}
              title="Panel configuration"
              className="hover:bg-gray-200"
            />
            
            {/* Remove Button */}
            <Button
              type="text"
              size="small"
              icon={<CloseOutlined />}
              onClick={handleRemove}
              title="Remove panel"
              className="hover:bg-red-100 hover:text-red-600"
            />
          </div>
        </div>

        {/* Panel Content */}
        <div 
          ref={panelContentRef}
          className="plc-grid-item-content flex-1 p-4 overflow-hidden"
          style={{ minHeight: '200px' }}
        >
          {renderPanelContent(false)}
        </div>
      </div>

      {/* Fullscreen Modal */}
      <FullScreenModal
        isOpen={isFullScreen}
        onClose={() => setIsFullScreen(false)}
        title={data.title}
      >
        <div className="h-full w-full">
          {renderPanelContent(true)}
        </div>
      </FullScreenModal>
    </>
  );
};

export default PLCGridItem;

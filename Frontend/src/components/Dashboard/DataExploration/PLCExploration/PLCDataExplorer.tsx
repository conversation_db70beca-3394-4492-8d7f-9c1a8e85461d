import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  Button,
  Empty,
  Spin
} from 'antd';
import { 
  UpOutlined, 
  DownOutlined, 
  LineChartOutlined,
  SettingOutlined 
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import PLCDataExplorerConfigDrawer from './PLCDataExplorerConfigDrawer';
import dayjs from 'dayjs';
import './styles/PLCStyles.css';

interface PLCDataExplorerProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  onDataLoad?: (data: any) => void;
}

const PLCDataExplorer: React.FC<PLCDataExplorerProps> = ({
  isCollapsed,
  onToggleCollapse,
  onDataLoad
}) => {
  const [configDrawerOpen, setConfigDrawerOpen] = useState(false);
  const [explorerData, setExplorerData] = useState<any>(null);
  const [configurationLoading, setConfigurationLoading] = useState(false);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [isChartReady, setIsChartReady] = useState(false);
  const [chartUpdateTrigger, setChartUpdateTrigger] = useState(0);
  const [brushSelection, setBrushSelection] = useState<any>(null);

  // Debug brush selection changes
  useEffect(() => {
    console.log('🎨 Brush selection changed:', brushSelection);
  }, [brushSelection]);

  const containerRef = useRef<HTMLDivElement>(null);
  const echartsRef = useRef<any>(null);

  // Monitor container size changes
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setContainerSize({ width: offsetWidth, height: offsetHeight });
        setIsChartReady(offsetWidth > 0 && offsetHeight > 0);
      }
    };

    updateSize();
    
    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [isCollapsed]);

  // Handle configuration data load
  const handleConfigDataLoad = (data: any) => {
    setExplorerData(data);
    setConfigurationLoading(false); // Stop loading when data is loaded
    
    // Notify parent component about data load
    if (onDataLoad) {
      onDataLoad(data);
    }

    // Force chart re-render
    setTimeout(() => {
      setChartUpdateTrigger(prev => prev + 1);
    }, 100);
  };

  // Handle configuration drawer open
  const handleOpenConfig = () => {
    setConfigDrawerOpen(true);
    setConfigurationLoading(true);
  };

  // Handle configuration drawer close
  const handleCloseConfig = () => {
    setConfigDrawerOpen(false);
    setConfigurationLoading(false);
  };

  // Force chart re-render when data changes (but NOT when brush selection changes)
  useEffect(() => {
    if (echartsRef.current) {
      const chartInstance = echartsRef.current.getEchartsInstance();
      if (chartInstance) {
        chartInstance.dispose();
      }
    }

    const timeoutId = setTimeout(() => {
      setChartUpdateTrigger(prev => prev + 1);
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [explorerData?.selectedBatches?.join(',')]); // Removed brushSelection dependency

  // Create plot data from API response (similar to BatchComparisonPanel)
  const plotData = useMemo(() => {
    if (!explorerData || !explorerData.columnOptions) {
      return [];
    }

    const columnOptions = explorerData.columnOptions;
    const qualityLookup = (explorerData.qualityBatchIds || []).reduce((acc: any, batch: any) => {
      acc[batch.batch_id] = batch;
      return acc;
    }, {});

    const result: any[] = [];
    
    // Get only the first available column except DateTime (not all columns)
    const availableColumns = Object.keys(columnOptions).filter(col => col !== 'DateTime');
    const firstColumn = availableColumns.length > 0 ? availableColumns[0] : null;
    
    if (firstColumn) {
      const columnName = firstColumn;
      const columnData = columnOptions[columnName];

      if (columnData?.series && Array.isArray(columnData.series)) {
        columnData.series.forEach((series: any, seriesIndex: number) => {
          if (series && series.data && series.data.length > 0) {
            const batchId = series.name || `Batch_${seriesIndex + 1}`;
            
            // Get quality information for this batch
            const qualityInfo = qualityLookup[batchId] || {
              batch_quality: 0,
              is_batch_good: false
            };

            // Calculate start and end times for this batch
            const timestamps = series.data.map((point: any) => new Date(point[0]).getTime());
            const startTime = Math.min(...timestamps);
            const endTime = Math.max(...timestamps);

            result.push({
              name: `${columnName} - ${batchId}`,
              data: series.data,
              groupValue: series.name || null,
              originalColumn: columnName,
              lineStyle: series.lineStyle || { width: 2 },
              symbolSize: series.symbolSize || 4,
              color: series.color || series.lineStyle?.color || null,
              seriesIndex: seriesIndex,
              columnName: columnName,
              // Enhanced batch metadata
              batchId: batchId,
              batchStartTime: startTime,
              batchEndTime: endTime,
              batchQuality: qualityInfo.batch_quality,
              isBatchGood: qualityInfo.is_batch_good
            });
          }
        });
      }
    }

    return result;
  }, [explorerData]);

  // Create ECharts option (similar to BatchComparisonPanel)
  const createChartOption = useMemo(() => {
    if (!plotData || plotData.length === 0) {
      return {
        series: [],
        xAxis: { type: 'time' },
        yAxis: { type: 'value' },
        hasData: false
      };
    }

    // Since we only show first column, use simple single chart approach
    const firstColumnName = plotData.length > 0 ? plotData[0].columnName : 'Value';

    // Calculate Y-axis range for the single column
    let min = Infinity;
    let max = -Infinity;
    
    plotData.forEach((series: any) => {
      if (series.data && Array.isArray(series.data)) {
        series.data.forEach((point: any) => {
          if (point && Array.isArray(point) && typeof point[1] === 'number') {
            min = Math.min(min, point[1]);
            max = Math.max(max, point[1]);
          }
        });
      }
    });

    const yAxisRange = min !== Infinity && max !== -Infinity ? {
      min: min - (max - min) * 0.05,
      max: max + (max - min) * 0.05
    } : null;

    const chartOption = {
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' },
        appendToBody: true,
        className: 'batch-comparison-tooltip',
        extraCssText: 'z-index: 9999; position: fixed;',
        formatter: function(params: any) {
          if (!params || params.length === 0) return '';
          
          // Group tooltip content by batch
          const batchGroups: {[key: string]: any} = {};
          
          params.forEach((param: any) => {
            const seriesData = plotData.find(p => p.name === param.seriesName);
            
            if (seriesData) {
              const batchId = seriesData.batchId;
              
              if (!batchGroups[batchId]) {
                batchGroups[batchId] = {
                  batchId: batchId,
                  startTime: new Date(seriesData.batchStartTime).toLocaleString(),
                  endTime: new Date(seriesData.batchEndTime).toLocaleString(),
                  quality: seriesData.batchQuality,
                  isGood: seriesData.isBatchGood,
                  series: []
                };
              }
              
              batchGroups[batchId].series.push({
                name: param.seriesName,
                value: param.value,
                color: param.color,
                columnName: seriesData.columnName
              });
            }
          });
          
          let tooltipHtml = '';
          Object.values(batchGroups).forEach((batch: any) => {
            const qualityColor = batch.isGood ? '#52c41a' : '#ff4d4f';
            const qualityText = batch.isGood ? 'Good' : 'Bad';
            
            tooltipHtml += `
              <div style="
                background: linear-gradient(135deg, ${qualityColor}15, ${qualityColor}05);
                border: 1px solid ${qualityColor}40;
                border-radius: 8px;
                padding: 12px;
                margin-bottom: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              ">
                <div style="
                  font-weight: 600;
                  font-size: 14px;
                  color: ${qualityColor};
                  margin-bottom: 8px;
                  display: flex;
                  align-items: center;
                  gap: 6px;
                ">
                  <span style="color: #333;">Batch:</span> ${batch.batchId}
                </div>
               
               <div style="
                 display: grid;
                 grid-template-columns: auto 1fr;
                 gap: 4px 8px;
                 font-size: 12px;
                 color: #666;
                 margin-bottom: 8px;
               ">
                 <span style="font-weight: 500;">⏰ Start:</span>
                 <span>${batch.startTime}</span>
                 <span style="font-weight: 500;">⏰ End:</span>
                 <span>${batch.endTime}</span>
                 <span style="font-weight: 500;">🎯 Quality:</span>
                 <span style="color: ${qualityColor}; font-weight: 600;">
                   ${qualityText} (${batch.quality.toFixed(2)})
                 </span>
               </div>
               
               <div style="
                 border-top: 1px solid #f0f0f0;
                 padding-top: 8px;
                 font-size: 12px;
               ">
                 ${batch.series.map((s: any) => `
                   <div style="
                     display: flex;
                     justify-content: space-between;
                     align-items: center;
                     padding: 2px 0;
                   ">
                     <span style="
                       display: flex;
                       align-items: center;
                       gap: 6px;
                       color: #333;
                       font-weight: 500;
                     ">
                       <span style="
                         display: inline-block;
                         width: 8px;
                         height: 8px;
                         border-radius: 50%;
                         background: ${s.color};
                       "></span>
                       ${s.columnName}
                     </span>
                     <span style="
                       font-weight: 600;
                       color: #1890ff;
                     ">
                       ${Array.isArray(s.value) ? s.value[1]?.toFixed(2) : s.value?.toFixed(2)}
                     </span>
                   </div>
                 `).join('')}
               </div>
             </div>
           `;
          });
          
          return tooltipHtml;
        }
      },
      brush: {
        toolbox: ['rect', 'polygon', 'lineX', 'lineY', 'keep', 'clear'],
        xAxisIndex: 0,
        brushType: 'lineX',
        brushMode: 'single',
        brushStyle: {
          borderWidth: 2,
          color: 'rgba(24, 144, 255, 0.1)',
          borderColor: 'rgba(24, 144, 255, 0.8)'
        },
        z: 10000
      },
      toolbox: {
        show: true,
        feature: {
          brush: {
            type: ['lineX', 'clear']
          },
          dataZoom: {
            yAxisIndex: 'all'
          },
          restore: {},
          saveAsImage: {}
        },
        right: 20,
        top: 20
      },
      legend: {
        data: plotData.map(s => s.name),
        top: 10,
        type: 'scroll',
        itemGap: 8,
        textStyle: { fontSize: 11 }
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0],
          yAxisIndex: [], // Only horizontal zoom
          filterMode: 'filter'
        },
        {
          type: 'slider',
          xAxisIndex: [0],
          yAxisIndex: [], // Only horizontal zoom  
          filterMode: 'filter'
        }
      ],
      grid: {
        left: '60px',
        right: '40px',
        top: '15%',
        bottom: 100, // More space for brush area like in ECharts example
        containLabel: true
      },
      xAxis: {
        type: 'time',
        name: 'DateTime',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            width: 1,
            type: 'solid'
          }
        },
        axisLine: {
          show: true,
          lineStyle: { color: '#666' }
        },
        axisTick: { show: true },
        axisLabel: {
          show: true,
          fontSize: 11,
          color: '#666'
        }
      },
      yAxis: {
        type: 'value',
        name: firstColumnName,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        scale: false,
        silent: true, // Disable Y-axis interaction
        min: yAxisRange?.min,
        max: yAxisRange?.max,
        axisLabel: {
          formatter: function (value: any) {
            return typeof value === 'number' ? value.toFixed(2) : value;
          }
        }
      },
      series: plotData.map((s, seriesIndex) => {
        // Enhanced emphasis style similar to ECharts example
        const emphasisStyle = {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0,0,0,0.3)'
          }
        };

        return {
          name: s.name,
          type: 'line',
          data: s.data || [],
          smooth: false,
          symbol: 'circle',
          symbolSize: s.symbolSize || 4,
          lineStyle: {
            width: s.lineStyle?.width || 2,
            color: s.color || s.lineStyle?.color || '#1f77b4',
            opacity: 1.0 // Default opacity, will be updated by brush events
          },
          itemStyle: {
            color: s.color || s.lineStyle?.color || '#1f77b4',
            opacity: 1.0, // Default opacity, will be updated by brush events
            shadowBlur: 0,
            shadowColor: 'transparent'
          },
          emphasis: {
            ...emphasisStyle,
            focus: 'series',
            itemStyle: {
              ...emphasisStyle.itemStyle,
              shadowBlur: 12,
              shadowColor: 'rgba(24, 144, 255, 0.5)'
            }
          }
        };
      }),
      hasData: true
    };
    
    console.log('📊 Chart option created. HasData:', chartOption.hasData);
    console.log('📊 Brush config:', chartOption.brush);
    console.log('📊 Toolbox config:', chartOption.toolbox);
    
    return chartOption;
  }, [plotData]); // Removed brushSelection dependency to prevent re-renders

  // Simplified brush event setup - main handling is now in onChartReady
  useEffect(() => {
    console.log('🎯 Chart data updated. PlotData length:', plotData.length);
  }, [plotData]);

  return (
    <div className={`plc-data-explorer ${isCollapsed ? 'collapsed' : 'expanded'}`}>
      {/* Header Section - Always Visible */}
      <div 
        style={{
          background: '#ffffff',
          borderTop: '1px solid #e8e8e8',
          borderLeft: '1px solid #e8e8e8',
          borderRight: '1px solid #e8e8e8',
          borderRadius: '8px 8px 0 0',
          padding: '12px 16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 -2px 8px rgba(0,0,0,0.06)',
          position: 'relative',
          zIndex: 10
        }}
      >
        {/* Left Side - Title */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          fontSize: '16px',
          fontWeight: 600,
          color: '#333',
          cursor: 'pointer'
        }} onClick={onToggleCollapse}>
          <LineChartOutlined style={{ color: '#1890ff' }} />
          Batch Overview
          {isCollapsed ? <UpOutlined style={{ fontSize: '12px', color: '#999' }} /> : <DownOutlined style={{ fontSize: '12px', color: '#999' }} />}
        </div>

        {/* Right Side - Date Range & Config Button */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          {explorerData?.dateRange && (
            <span style={{ 
              fontSize: '13px', 
              color: '#1890ff',
              fontWeight: 500,
              background: '#f0f8ff',
              padding: '4px 8px',
              borderRadius: '4px',
              border: '1px solid #91d5ff'
            }}>
              📅 {dayjs(explorerData.dateRange.startDateTime).format('MMM DD')} - {dayjs(explorerData.dateRange.endDateTime).format('MMM DD, YYYY')}
            </span>
          )}
          {explorerData && (
            <span style={{ 
              fontSize: '12px', 
              color: '#666',
              marginRight: '4px'
            }}>
              {plotData.length} series • {explorerData.selectedBatches?.length || 0} batches
            </span>
          )}
          <Button
            type="primary"
            icon={<SettingOutlined />}
            onClick={handleOpenConfig}
            size="small"
            style={{
              borderRadius: '6px',
              boxShadow: '0 2px 4px rgba(24, 144, 255, 0.2)'
            }}
          >
            Configure
          </Button>
        </div>
      </div>

      {/* Explorer Content - Collapsible */}
      {!isCollapsed && (
        <div 
          className="explorer-content" 
          style={{ 
            background: 'white',
            border: '1px solid #e8e8e8',
            borderTop: 'none',
            height: 'calc(100% - 60px)',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}
        >
          {/* Chart Section */}
          <div 
            ref={containerRef}
            style={{ 
              flex: 1, 
              padding: '16px',
              minHeight: '200px',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            {configurationLoading ? (
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                flex: 1 
              }}>
                <Spin size="large" tip="Configuring explorer data..." />
              </div>
            ) : !explorerData ? (
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                flex: 1 
              }}>
                <Empty
                  image={<LineChartOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />}
                  description={
                    <div>
                      <h4 style={{ margin: '8px 0', color: '#666' }}>No Data Loaded</h4>
                      <p style={{ margin: 0, color: '#999', fontSize: '12px' }}>
                        Click "Configure" to load data and view batch comparison charts
                      </p>
                    </div>
                  }
                />
              </div>
            ) : (
              <div className="h-full w-full" style={{ flex: 1, minHeight: '200px' }}>
                {containerSize.width > 0 && isChartReady && (
                  <ReactECharts
                    key={`explorer-chart-${chartUpdateTrigger}`}
                    ref={echartsRef}
                    option={createChartOption}
                    style={{ width: '100%', height: '100%' }}
                    opts={{ renderer: 'canvas' }}
                    notMerge={true}
                    onChartReady={(chartInstance: any) => {
                      console.log('📊 Chart is ready for brush events');

                      // Attach brush events immediately when chart is ready
                      const handleBrushSelected = (params: any) => {
                        console.log('🎯 Brush Selection Event (Chart Ready):', JSON.stringify(params, null, 2));

                        if (params.batch && params.batch.length > 0) {
                          const brushComponent = params.batch[0];
                          const selectedBatchIds: string[] = [];
                          const selectedSeries: {[key: number]: number[]} = {};
                          let brushedIndices: string[] = [];
                          let hasValidSelection = false;

                          // Process selected data similar to ECharts example
                          for (let sIdx = 0; sIdx < brushComponent.selected.length; sIdx++) {
                            const selection = brushComponent.selected[sIdx];
                            if (selection && selection.dataIndex && selection.dataIndex.length > 0) {
                              const rawIndices = selection.dataIndex;
                              brushedIndices.push('[Series ' + sIdx + '] ' + rawIndices.join(', '));
                              hasValidSelection = true;

                              // Store selected data indices for this series
                              selectedSeries[sIdx] = rawIndices;

                              // Get the series data for this selection
                              const seriesData = plotData[sIdx];

                              if (seriesData && seriesData.batchId) {
                                if (!selectedBatchIds.includes(seriesData.batchId)) {
                                  selectedBatchIds.push(seriesData.batchId);
                                }
                              }
                            }
                          }

                          // Only update state if there's a valid selection
                          if (hasValidSelection) {
                            // Extract unique batch IDs from selected data
                            const uniqueBatchIds = Array.from(new Set(selectedBatchIds));

                            console.log('🔍 SELECTED DATA INDICES:', brushedIndices.join('\n'));
                            console.log('🔍 Selected Batch IDs from Horizontal Range:', uniqueBatchIds);

                            // Update chart option directly without triggering re-render
                            const currentOption = chartInstance.getOption();
                            const updatedSeries = currentOption.series.map((series: any, index: number) => {
                              const hasSelection = selectedSeries[index] && selectedSeries[index].length > 0;
                              const anySelectionExists = Object.values(selectedSeries).some((indices: any) => indices && indices.length > 0);
                              const shouldBlur = anySelectionExists && !hasSelection;

                              return {
                                ...series,
                                lineStyle: {
                                  ...series.lineStyle,
                                  opacity: shouldBlur ? 0.3 : 1.0
                                },
                                itemStyle: {
                                  ...series.itemStyle,
                                  opacity: shouldBlur ? 0.25 : 1.0
                                }
                              };
                            });

                            chartInstance.setOption({
                              series: updatedSeries
                            }, false); // false = don't merge, just update

                            // Store selection for logging but don't trigger re-render
                            console.log('🎨 Brush selection applied directly to chart');
                          } else {
                            console.log('🔍 No valid selection found, clearing highlights');
                            // Clear any existing highlights
                            const currentOption = chartInstance.getOption();
                            const clearedSeries = currentOption.series.map((series: any) => ({
                              ...series,
                              lineStyle: {
                                ...series.lineStyle,
                                opacity: 1.0
                              },
                              itemStyle: {
                                ...series.itemStyle,
                                opacity: 1.0
                              }
                            }));

                            chartInstance.setOption({
                              series: clearedSeries
                            }, false);
                          }
                        }
                      };

                      // Handle brush clear events
                      const handleBrush = (params: any) => {
                        console.log('🔍 Brush event:', params);
                        // If brush is being cleared or no areas selected
                        if (!params.areas || params.areas.length === 0) {
                          console.log('🔍 Clearing brush selection');
                          // Clear any existing highlights
                          const currentOption = chartInstance.getOption();
                          const clearedSeries = currentOption.series.map((series: any) => ({
                            ...series,
                            lineStyle: {
                              ...series.lineStyle,
                              opacity: 1.0
                            },
                            itemStyle: {
                              ...series.itemStyle,
                              opacity: 1.0
                            }
                          }));

                          chartInstance.setOption({
                            series: clearedSeries
                          }, false);
                        }
                      };

                      // Attach the event listeners immediately
                      chartInstance.on('brushSelected', handleBrushSelected);
                      chartInstance.on('brush', handleBrush);
                      console.log('✅ Brush event listeners attached on chart ready');
                    }}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Configuration Drawer */}
      <PLCDataExplorerConfigDrawer
        open={configDrawerOpen}
        onClose={handleCloseConfig}
        onDataLoad={handleConfigDataLoad}
      />
    </div>
  );
};

export default PLCDataExplorer; 
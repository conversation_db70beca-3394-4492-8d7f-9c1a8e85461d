import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  Button,
  Empty,
  Spin
} from 'antd';
import { 
  UpOutlined, 
  DownOutlined, 
  LineChartOutlined,
  SettingOutlined 
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import PLCDataExplorerConfigDrawer from './PLCDataExplorerConfigDrawer';
import dayjs from 'dayjs';
import './styles/PLCStyles.css';

interface PLCDataExplorerProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  onDataLoad?: (data: any) => void;
}

const PLCDataExplorer: React.FC<PLCDataExplorerProps> = ({
  isCollapsed,
  onToggleCollapse,
  onDataLoad
}) => {
  const [configDrawerOpen, setConfigDrawerOpen] = useState(false);
  const [explorerData, setExplorerData] = useState<any>(null);
  const [configurationLoading, setConfigurationLoading] = useState(false);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [isChartReady, setIsChartReady] = useState(false);
  const [chartUpdateTrigger, setChartUpdateTrigger] = useState(0);
  const [brushSelection, setBrushSelection] = useState<any>(null);

  // Debug brush selection changes
  useEffect(() => {
    console.log('🎨 Brush selection changed:', brushSelection);
  }, [brushSelection]);

  const containerRef = useRef<HTMLDivElement>(null);
  const echartsRef = useRef<any>(null);

  // Monitor container size changes
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setContainerSize({ width: offsetWidth, height: offsetHeight });
        setIsChartReady(offsetWidth > 0 && offsetHeight > 0);
      }
    };

    updateSize();
    
    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [isCollapsed]);

  // Handle configuration data load
  const handleConfigDataLoad = (data: any) => {
    setExplorerData(data);
    setConfigurationLoading(false); // Stop loading when data is loaded
    
    // Notify parent component about data load
    if (onDataLoad) {
      onDataLoad(data);
    }

    // Force chart re-render
    setTimeout(() => {
      setChartUpdateTrigger(prev => prev + 1);
    }, 100);
  };

  // Handle configuration drawer open
  const handleOpenConfig = () => {
    setConfigDrawerOpen(true);
    setConfigurationLoading(true);
  };

  // Handle configuration drawer close
  const handleCloseConfig = () => {
    setConfigDrawerOpen(false);
    setConfigurationLoading(false);
  };

  // Force chart re-render when data changes
  useEffect(() => {
    if (echartsRef.current) {
      const chartInstance = echartsRef.current.getEchartsInstance();
      if (chartInstance) {
        chartInstance.dispose();
      }
    }

    const timeoutId = setTimeout(() => {
      setChartUpdateTrigger(prev => prev + 1);
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [explorerData?.selectedBatches?.join(','), brushSelection]);

  // Create plot data from API response (similar to BatchComparisonPanel)
  const plotData = useMemo(() => {
    if (!explorerData || !explorerData.columnOptions) {
      return [];
    }

    const columnOptions = explorerData.columnOptions;
    const qualityLookup = (explorerData.qualityBatchIds || []).reduce((acc: any, batch: any) => {
      acc[batch.batch_id] = batch;
      return acc;
    }, {});

    const result: any[] = [];
    
    // Get only the first available column except DateTime (not all columns)
    const availableColumns = Object.keys(columnOptions).filter(col => col !== 'DateTime');
    const firstColumn = availableColumns.length > 0 ? availableColumns[0] : null;
    
    if (firstColumn) {
      const columnName = firstColumn;
      const columnData = columnOptions[columnName];

      if (columnData?.series && Array.isArray(columnData.series)) {
        columnData.series.forEach((series: any, seriesIndex: number) => {
          if (series && series.data && series.data.length > 0) {
            const batchId = series.name || `Batch_${seriesIndex + 1}`;
            
            // Get quality information for this batch
            const qualityInfo = qualityLookup[batchId] || {
              batch_quality: 0,
              is_batch_good: false
            };

            // Calculate start and end times for this batch
            const timestamps = series.data.map((point: any) => new Date(point[0]).getTime());
            const startTime = Math.min(...timestamps);
            const endTime = Math.max(...timestamps);

            result.push({
              name: `${columnName} - ${batchId}`,
              data: series.data,
              groupValue: series.name || null,
              originalColumn: columnName,
              lineStyle: series.lineStyle || { width: 2 },
              symbolSize: series.symbolSize || 4,
              color: series.color || series.lineStyle?.color || null,
              seriesIndex: seriesIndex,
              columnName: columnName,
              // Enhanced batch metadata
              batchId: batchId,
              batchStartTime: startTime,
              batchEndTime: endTime,
              batchQuality: qualityInfo.batch_quality,
              isBatchGood: qualityInfo.is_batch_good
            });
          }
        });
      }
    }

    return result;
  }, [explorerData]);

  // Create ECharts option (similar to BatchComparisonPanel)
  const createChartOption = useMemo(() => {
    if (!plotData || plotData.length === 0) {
      return {
        series: [],
        xAxis: { type: 'time' },
        yAxis: { type: 'value' },
        hasData: false
      };
    }

    // Since we only show first column, use simple single chart approach
    const firstColumnName = plotData.length > 0 ? plotData[0].columnName : 'Value';

    // Calculate Y-axis range for the single column
    let min = Infinity;
    let max = -Infinity;
    
    plotData.forEach((series: any) => {
      if (series.data && Array.isArray(series.data)) {
        series.data.forEach((point: any) => {
          if (point && Array.isArray(point) && typeof point[1] === 'number') {
            min = Math.min(min, point[1]);
            max = Math.max(max, point[1]);
          }
        });
      }
    });

    const yAxisRange = min !== Infinity && max !== -Infinity ? {
      min: min - (max - min) * 0.05,
      max: max + (max - min) * 0.05
    } : null;

    const chartOption = {
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' },
        appendToBody: true,
        className: 'batch-comparison-tooltip',
        extraCssText: 'z-index: 9999; position: fixed;',
        formatter: function(params: any) {
          if (!params || params.length === 0) return '';
          
          // Group tooltip content by batch
          const batchGroups: {[key: string]: any} = {};
          
          params.forEach((param: any) => {
            const seriesData = plotData.find(p => p.name === param.seriesName);
            
            if (seriesData) {
              const batchId = seriesData.batchId;
              
              if (!batchGroups[batchId]) {
                batchGroups[batchId] = {
                  batchId: batchId,
                  startTime: new Date(seriesData.batchStartTime).toLocaleString(),
                  endTime: new Date(seriesData.batchEndTime).toLocaleString(),
                  quality: seriesData.batchQuality,
                  isGood: seriesData.isBatchGood,
                  series: []
                };
              }
              
              batchGroups[batchId].series.push({
                name: param.seriesName,
                value: param.value,
                color: param.color,
                columnName: seriesData.columnName
              });
            }
          });
          
          let tooltipHtml = '';
          Object.values(batchGroups).forEach((batch: any) => {
            const qualityColor = batch.isGood ? '#52c41a' : '#ff4d4f';
            const qualityText = batch.isGood ? 'Good' : 'Bad';
            
            tooltipHtml += `
              <div style="
                background: linear-gradient(135deg, ${qualityColor}15, ${qualityColor}05);
                border: 1px solid ${qualityColor}40;
                border-radius: 8px;
                padding: 12px;
                margin-bottom: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
              ">
                <div style="
                  font-weight: 600;
                  font-size: 14px;
                  color: ${qualityColor};
                  margin-bottom: 8px;
                  display: flex;
                  align-items: center;
                  gap: 6px;
                ">
                  <span style="color: #333;">Batch:</span> ${batch.batchId}
                </div>
               
               <div style="
                 display: grid;
                 grid-template-columns: auto 1fr;
                 gap: 4px 8px;
                 font-size: 12px;
                 color: #666;
                 margin-bottom: 8px;
               ">
                 <span style="font-weight: 500;">⏰ Start:</span>
                 <span>${batch.startTime}</span>
                 <span style="font-weight: 500;">⏰ End:</span>
                 <span>${batch.endTime}</span>
                 <span style="font-weight: 500;">🎯 Quality:</span>
                 <span style="color: ${qualityColor}; font-weight: 600;">
                   ${qualityText} (${batch.quality.toFixed(2)})
                 </span>
               </div>
               
               <div style="
                 border-top: 1px solid #f0f0f0;
                 padding-top: 8px;
                 font-size: 12px;
               ">
                 ${batch.series.map((s: any) => `
                   <div style="
                     display: flex;
                     justify-content: space-between;
                     align-items: center;
                     padding: 2px 0;
                   ">
                     <span style="
                       display: flex;
                       align-items: center;
                       gap: 6px;
                       color: #333;
                       font-weight: 500;
                     ">
                       <span style="
                         display: inline-block;
                         width: 8px;
                         height: 8px;
                         border-radius: 50%;
                         background: ${s.color};
                       "></span>
                       ${s.columnName}
                     </span>
                     <span style="
                       font-weight: 600;
                       color: #1890ff;
                     ">
                       ${Array.isArray(s.value) ? s.value[1]?.toFixed(2) : s.value?.toFixed(2)}
                     </span>
                   </div>
                 `).join('')}
               </div>
             </div>
           `;
          });
          
          return tooltipHtml;
        }
      },
      brush: {
        xAxisIndex: [0], // Only horizontal brush on X-axis  
        yAxisIndex: [], // Disable vertical brush
        brushType: 'lineX', // Only horizontal selection
        brushMode: 'single', // Single selection mode
        brushStyle: {
          borderWidth: 2,
          color: 'rgba(24, 144, 255, 0.1)',
          borderColor: 'rgba(24, 144, 255, 0.8)'
        },
        z: 10000 // High z-index to prevent overlap issues
      },
      toolbox: {
        show: true,
        feature: {
          brush: {
            type: ['lineX', 'clear'] // Enable horizontal selection and clear
          },
          dataZoom: {
            yAxisIndex: 'all'
          },
          restore: {},
          saveAsImage: {}
        },
        right: 20,
        top: 20
      },
      legend: {
        data: plotData.map(s => s.name),
        top: 10,
        type: 'scroll',
        itemGap: 8,
        textStyle: { fontSize: 11 }
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0],
          yAxisIndex: [], // Only horizontal zoom
          filterMode: 'filter'
        },
        {
          type: 'slider',
          xAxisIndex: [0],
          yAxisIndex: [], // Only horizontal zoom  
          filterMode: 'filter'
        }
      ],
      grid: {
        left: '60px',
        right: '40px', // Less space since no vertical slider
        top: '15%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'time',
        name: 'DateTime',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            width: 1,
            type: 'solid'
          }
        },
        axisLine: {
          show: true,
          lineStyle: { color: '#666' }
        },
        axisTick: { show: true },
        axisLabel: {
          show: true,
          fontSize: 11,
          color: '#666'
        }
      },
      yAxis: {
        type: 'value',
        name: firstColumnName,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        scale: false,
        silent: true, // Disable Y-axis interaction
        min: yAxisRange?.min,
        max: yAxisRange?.max,
        axisLabel: {
          formatter: function (value: any) {
            return typeof value === 'number' ? value.toFixed(2) : value;
          }
        }
      },
      series: plotData.map((s, seriesIndex) => {
        // Determine if this series has selected data
        const hasSelection = brushSelection && brushSelection.selectedSeries && 
                           brushSelection.selectedSeries[seriesIndex] && 
                           brushSelection.selectedSeries[seriesIndex].length > 0;
        
        // Determine if ANY series has selection (for blurring non-selected)
        const anySelectionExists = brushSelection && brushSelection.selectedSeries && 
                                 Object.values(brushSelection.selectedSeries).some((indices: any) => indices && indices.length > 0);
        
        // Calculate opacity based on selection state
        const isSelected = hasSelection;
        const shouldBlur = anySelectionExists && !isSelected;
        const opacity = shouldBlur ? 0.25 : 1.0;
        const lineOpacity = shouldBlur ? 0.3 : 1.0;
        
        return {
          name: s.name,
          type: 'line',
          data: s.data || [],
          smooth: false,
          symbol: 'circle',
          symbolSize: s.symbolSize || 4,
          lineStyle: {
            width: s.lineStyle?.width || 2,
            color: s.color || s.lineStyle?.color || '#1f77b4',
            opacity: lineOpacity
          },
          itemStyle: {
            color: s.color || s.lineStyle?.color || '#1f77b4',
            opacity: opacity,
            shadowBlur: isSelected ? 8 : 0,
            shadowColor: isSelected ? 'rgba(24, 144, 255, 0.3)' : 'transparent'
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 12,
              shadowColor: 'rgba(24, 144, 255, 0.5)'
            }
          }
        };
      }),
      hasData: true
    };
    
    console.log('📊 Chart option created. HasData:', chartOption.hasData);
    console.log('📊 Brush config:', chartOption.brush);
    console.log('📊 Toolbox config:', chartOption.toolbox);
    
    return chartOption;
  }, [plotData, brushSelection]);

  // Add brush event listener for horizontal selection with batch ID logging
  useEffect(() => {
    console.log('🎯 Setting up brush events. PlotData length:', plotData.length);
    console.log('🎯 EChartsRef current:', !!echartsRef.current);
    
    if (echartsRef.current && plotData.length > 0) {
      const chartInstance = echartsRef.current.getEchartsInstance();
      console.log('🎯 Chart instance:', !!chartInstance);
      
      const handleBrushSelected = (params: any) => {
        console.log('🎯 Brush Selection Event:', params);
        
        if (params.batch && params.batch.length > 0) {
          const brushComponent = params.batch[0];
          const selectedBatchIds: string[] = [];
          const selectedSeries: {[key: number]: number[]} = {};
          
          // Iterate through all selected series
          brushComponent.selected.forEach((selection: any, seriesIndex: number) => {
            if (selection.dataIndex && selection.dataIndex.length > 0) {
              // Store selected data indices for this series
              selectedSeries[seriesIndex] = selection.dataIndex;
              
              // Get the series data for this selection
              const seriesData = plotData[seriesIndex];
              
              if (seriesData && seriesData.batchId) {
                if (!selectedBatchIds.includes(seriesData.batchId)) {
                  selectedBatchIds.push(seriesData.batchId);
                }
              }
            }
          });
          
          // Extract unique batch IDs from selected data
          const uniqueBatchIds = Array.from(new Set(selectedBatchIds));
          
          console.log('🔍 Selected Batch IDs from Horizontal Range:', uniqueBatchIds);
          console.log('📊 Selection Details:', {
            totalDataPoints: brushComponent.selected.reduce((sum: number, sel: any) => sum + (sel.dataIndex?.length || 0), 0),
            batchIds: uniqueBatchIds,
            selectionAreas: params.batch.length
          });
          
          // Update brush selection state to trigger highlighting/blurring
          setBrushSelection({
            selectedSeries: selectedSeries,
            selectedBatchIds: uniqueBatchIds
          });
        } else {
          // No selection - clear highlighting
          setBrushSelection(null);
        }
      };
      
      // Handle brush events (more comprehensive)
      const handleBrush = (params: any) => {
        console.log('🔍 Brush event:', params);
        // If brush is being cleared or no areas selected
        if (!params.areas || params.areas.length === 0) {
          console.log('🔍 Clearing brush selection');
          setBrushSelection(null);
        }
      };
      
      const handleBrushEnd = (params: any) => {
        console.log('🔍 Brush end event:', params);
      };
      
      // Remove existing listeners and add new ones
      chartInstance.off('brushSelected');
      chartInstance.off('brush');
      chartInstance.off('brushEnd');
      
      chartInstance.on('brushSelected', handleBrushSelected);
      chartInstance.on('brush', handleBrush);
      chartInstance.on('brushEnd', handleBrushEnd);
      
      console.log('✅ Brush event listeners attached');
      
      return () => {
        if (chartInstance && !chartInstance.isDisposed()) {
          chartInstance.off('brushSelected', handleBrushSelected);
          chartInstance.off('brush', handleBrush);
          chartInstance.off('brushEnd', handleBrushEnd);
          console.log('🧹 Brush event listeners cleaned up');
        }
      };
    }
  }, [plotData]);

  return (
    <div className={`plc-data-explorer ${isCollapsed ? 'collapsed' : 'expanded'}`}>
      {/* Header Section - Always Visible */}
      <div 
        style={{
          background: '#ffffff',
          borderTop: '1px solid #e8e8e8',
          borderLeft: '1px solid #e8e8e8',
          borderRight: '1px solid #e8e8e8',
          borderRadius: '8px 8px 0 0',
          padding: '12px 16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 -2px 8px rgba(0,0,0,0.06)',
          position: 'relative',
          zIndex: 10
        }}
      >
        {/* Left Side - Title */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          fontSize: '16px',
          fontWeight: 600,
          color: '#333',
          cursor: 'pointer'
        }} onClick={onToggleCollapse}>
          <LineChartOutlined style={{ color: '#1890ff' }} />
          Batch Overview
          {isCollapsed ? <UpOutlined style={{ fontSize: '12px', color: '#999' }} /> : <DownOutlined style={{ fontSize: '12px', color: '#999' }} />}
        </div>

        {/* Right Side - Date Range & Config Button */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          {explorerData?.dateRange && (
            <span style={{ 
              fontSize: '13px', 
              color: '#1890ff',
              fontWeight: 500,
              background: '#f0f8ff',
              padding: '4px 8px',
              borderRadius: '4px',
              border: '1px solid #91d5ff'
            }}>
              📅 {dayjs(explorerData.dateRange.startDateTime).format('MMM DD')} - {dayjs(explorerData.dateRange.endDateTime).format('MMM DD, YYYY')}
            </span>
          )}
          {explorerData && (
            <span style={{ 
              fontSize: '12px', 
              color: '#666',
              marginRight: '4px'
            }}>
              {plotData.length} series • {explorerData.selectedBatches?.length || 0} batches
            </span>
          )}
          <Button
            type="primary"
            icon={<SettingOutlined />}
            onClick={handleOpenConfig}
            size="small"
            style={{
              borderRadius: '6px',
              boxShadow: '0 2px 4px rgba(24, 144, 255, 0.2)'
            }}
          >
            Configure
          </Button>
        </div>
      </div>

      {/* Explorer Content - Collapsible */}
      {!isCollapsed && (
        <div 
          className="explorer-content" 
          style={{ 
            background: 'white',
            border: '1px solid #e8e8e8',
            borderTop: 'none',
            height: 'calc(100% - 60px)',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}
        >
          {/* Chart Section */}
          <div 
            ref={containerRef}
            style={{ 
              flex: 1, 
              padding: '16px',
              minHeight: '200px',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            {configurationLoading ? (
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                flex: 1 
              }}>
                <Spin size="large" tip="Configuring explorer data..." />
              </div>
            ) : !explorerData ? (
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                flex: 1 
              }}>
                <Empty
                  image={<LineChartOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />}
                  description={
                    <div>
                      <h4 style={{ margin: '8px 0', color: '#666' }}>No Data Loaded</h4>
                      <p style={{ margin: 0, color: '#999', fontSize: '12px' }}>
                        Click "Configure" to load data and view batch comparison charts
                      </p>
                    </div>
                  }
                />
              </div>
            ) : (
              <div className="h-full w-full" style={{ flex: 1, minHeight: '200px' }}>
                {containerSize.width > 0 && isChartReady && (
                  <ReactECharts
                    key={`explorer-chart-${chartUpdateTrigger}-${brushSelection ? 'selected' : 'normal'}`}
                    ref={echartsRef}
                    option={createChartOption}
                    style={{ width: '100%', height: '100%' }}
                    opts={{ renderer: 'canvas' }}
                    notMerge={true}
                    onChartReady={() => {
                      console.log('📊 Chart is ready for brush events');
                    }}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Configuration Drawer */}
      <PLCDataExplorerConfigDrawer
        open={configDrawerOpen}
        onClose={handleCloseConfig}
        onDataLoad={handleConfigDataLoad}
      />
    </div>
  );
};

export default PLCDataExplorer; 
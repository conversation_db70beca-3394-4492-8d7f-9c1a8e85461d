import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  Drawer,
  Form,
  Button,
  DatePicker,
  Select,
  Checkbox,
  Typography,
  message,
  Spin
} from 'antd';
import { CloseOutlined, SettingOutlined } from '@ant-design/icons';
import {
  PLCPanelConfigDrawerProps,
  PLCPanelConfiguration
} from './types/PLCTypes';
import { postRequest } from '../../../../utils/apiHandler';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const PLCPanelConfigDrawer: React.FC<PLCPanelConfigDrawerProps> = ({
  open,
  onClose,
  panelId,
  configuration,
  onConfigurationSave
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [windowMode, setWindowMode] = useState(false);
   const [selectedGroup, setSelectedGroup] = useState<string>('BatchId');
  
  // Get selected systems from Redux state
  const selectSystems = useSelector((state: any) => state.systems.systems);
  
  // Helper function to extract system names from selected systems
  const getSystemNames = () => {
    if (!selectSystems || !selectSystems.length || !selectSystems[0]?.systems) {
      return [];
    }
    
    return selectSystems[0].systems.map((system: any) => ({
      systemId: system.systemId,
      systemName: system.systemName
    }));
  };
  const [selectedTargets, setSelectedTargets] = useState<string[]>([]);
  const [windowLoading, setWindowLoading] = useState(false);
  const [fullScreenLoading, setFullScreenLoading] = useState(false);
  const [basicSettingsLoading, setBasicSettingsLoading] = useState(false);

  // New state for batch management
  const [availableBatches, setAvailableBatches] = useState<string[]>([]);
  const [qualityBatchIds, setQualityBatchIds] = useState<Array<{
    batch_id: string;
    batch_quality: number;
    is_batch_good: boolean;
  }>>([]);
  const [selectedBatches, setSelectedBatches] = useState<string[]>([]);
  const [compareLimit, setCompareLimit] = useState<number>(5);
  const [batchMetaLoading, setBatchMetaLoading] = useState(false);
  const [isDataLoaded, setIsDataLoaded] = useState(false);

  // Recent searches management
  const [recentSearches, setRecentSearches] = useState<Array<{label: string, value: [any, any]}>>([]);
  const RECENT_SEARCHES_KEY = 'plc_recent_date_searches';
  const MAX_RECENT_SEARCHES = 5;

  // Master loading state that combines all critical operations
  const isMasterLoading = batchMetaLoading || (loading && !batchMetaLoading) || basicSettingsLoading || windowLoading;
  
  // Get current loading message based on active operation
  const getLoadingMessage = () => {
    if (batchMetaLoading) return 'Fetching batch metadata...';
    if (loading && !batchMetaLoading) return 'Loading data...';
    if (basicSettingsLoading) return 'Applying basic settings...';
    if (windowLoading) return 'Applying window settings...';
    return 'Processing...';
  };

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem(RECENT_SEARCHES_KEY);
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        const validSearches = parsed.map((search: any) => ({
          label: search.label,
          value: [dayjs(search.value[0]), dayjs(search.value[1])] as [any, any]
        }));
        setRecentSearches(validSearches);
      } catch (error) {
        console.error('Error loading recent searches:', error);
      }
    }
  }, []);

  // Save a new date range to recent searches
  const saveToRecentSearches = (startDateTime: any, endDateTime: any) => {
    const newSearch = {
      label: `${startDateTime.format('MMM DD')} - ${endDateTime.format('MMM DD, YYYY')}`,
      value: [startDateTime, endDateTime] as [any, any]
    };

    const existing = recentSearches.filter(search => 
      search.label !== newSearch.label
    );
    
    const updated = [newSearch, ...existing].slice(0, MAX_RECENT_SEARCHES);
    setRecentSearches(updated);

    // Save to localStorage
    const toSave = updated.map(search => ({
      label: search.label,
      value: [search.value[0].toISOString(), search.value[1].toISOString()]
    }));
    
    localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(toSave));
  };

  // Create presets for RangePicker (common ranges + recent searches)
  const createDatePresets = () => {
    const commonPresets = [
      {
        label: 'Last 24 hours',
        value: () => [dayjs().subtract(1, 'day'), dayjs()] as [any, any]
      },
      {
        label: 'Last 7 days', 
        value: () => [dayjs().subtract(7, 'days'), dayjs()] as [any, any]
      },
      {
        label: 'Last 30 days',
        value: () => [dayjs().subtract(30, 'days'), dayjs()] as [any, any]
      },
      {
        label: 'This month',
        value: () => [dayjs().startOf('month'), dayjs()] as [any, any]
      }
    ];

    // Add separator if there are recent searches
    const allPresets = [...commonPresets];
    
    if (recentSearches.length > 0) {
      // Add recent searches without separator (separator causes issues)
      const recentPresets = recentSearches.map(search => ({
        label: `📅 ${search.label}`,
        value: () => search.value as [any, any]
      }));
      
      allPresets.push(...recentPresets);
    }

    return allPresets;
  };

  // Function to fetch batch metadata
  const fetchBatchMetadata = async (startDateTime: any, endDateTime: any) => {
    setBatchMetaLoading(true);
    try {
      const systems = getSystemNames();
      const systemNames = systems.map((s: any) => s.systemName).join(' + ') || '';
      
      const apiPayload = {
        start_datetime: startDateTime.format('YYYY-MM-DD HH:mm'),
        end_datetime: endDateTime.format('YYYY-MM-DD HH:mm'),
        system: systemNames
      };

      const response = await postRequest('/file/explore-plc/get-batch-meta', apiPayload);

      if (response.data.data) {
        const { batch_ids, quality_batch_ids, compare_limit } = response.data.data;
        
        // Handle new quality_batch_ids structure if available
        if (quality_batch_ids && Array.isArray(quality_batch_ids)) {
          setQualityBatchIds(quality_batch_ids);
          // Extract batch IDs for backward compatibility
          setAvailableBatches(quality_batch_ids.map(batch => batch.batch_id));
        } else {
          // Fallback to old structure
          setAvailableBatches(batch_ids || []);
          setQualityBatchIds([]);
        }
        
        setCompareLimit(compare_limit || 5);
        setSelectedBatches([]); // Reset selected batches
        // Don't show success message - just proceed to batch selection
        return true; // Success
      } else {
        message.error('Failed to fetch batch metadata');
        setAvailableBatches([]);
        setQualityBatchIds([]);
        setCompareLimit(5);
        return false; // Failure
      }
    } catch (error) {
      console.error('Error fetching batch metadata:', error);
      message.error('Error fetching batch metadata');
      setAvailableBatches([]);
      setQualityBatchIds([]);
      setCompareLimit(5);
      return false; // Failure
    } finally {
      setBatchMetaLoading(false);
    }
  };

  // Function to load data with selected batches
  const handleLoadData = async () => {
    if (!panelId || selectedBatches.length === 0) {
      message.error('Please select at least one batch');
      return;
    }

    const dateTimeRange = form.getFieldValue('dateTimeRange');
    if (!dateTimeRange || !dateTimeRange[0] || !dateTimeRange[1]) {
      message.error('Date range is required');
      return;
    }

    setLoading(true);
    try {
      const [startDateTime, endDateTime] = dateTimeRange;

      const systems = getSystemNames();
      const systemNames = systems.map((s: any) => s.systemName).join(' + ') || '';
      
      const apiPayload = {
        start_datetime: startDateTime.format('YYYY-MM-DD HH:mm:ss'),
        end_datetime: endDateTime.format('YYYY-MM-DD HH:mm:ss'),
        batch_ids: selectedBatches,
        x_axis: 'DateTime', // Default X-axis
        y_axis: [], // Will be populated from API response
        identifier: 'BatchId', // Default identifier should be BatchId
        compare_limit: compareLimit,
        system: systemNames
      };

      const response = await postRequest('/file/explore-plc/batch-comparison', apiPayload);

      if (response.data.data) {
        const apiData = response.data.data;



        // Get available columns from API response - now from columnOptions
        const columnOptions = apiData.columnOptions || {};
        const availableColumns = Object.keys(columnOptions);

        // Auto-select first column for Y-axis when data loads
        const firstColumn = availableColumns.length > 0 ? availableColumns[0] : null;

        // Update configuration with loaded data and auto-selected first column
        const loadedConfiguration: PLCPanelConfiguration = {
          dataRange: {
            startDate: startDateTime.format('YYYY-MM-DD'),
            endDate: endDateTime.format('YYYY-MM-DD'),
            startTime: startDateTime.format('HH:mm'),
            endTime: endDateTime.format('HH:mm'),
          },
          basic: {
            xAxisColumn: 'DateTime',
            selectedColumns: {
              indices: firstColumn ? [0] : [],
              headers: firstColumn ? [firstColumn] : []
            },
            group: 'BatchId', // Default to BatchId instead of None
          },
          advanced: {
            windowMode: false,
          },
          title: firstColumn ? `Batch Comparison - ${firstColumn}` : 'Batch Comparison - No Data Selected',
          panelType: 'BatchComparisonPanel' as any,
          lastModified: new Date().toISOString(),
          apiData: {
            ...apiData,
            selectedBatches: selectedBatches,
            availableBatches: availableBatches,
            qualityBatchIds: qualityBatchIds,
            compareLimit: compareLimit,
            columns: availableColumns
          }
        };

        onConfigurationSave(panelId, loadedConfiguration);
        setIsDataLoaded(true);

        // Update form with loaded data and auto-selected first column
        form.setFieldsValue({
          xAxisColumn: 'DateTime',
          yAxis: firstColumn ? [firstColumn] : [],
          group: 'BatchId' // Default to BatchId
        });

        // Save configuration with auto-selected column to trigger chart rendering
        if (firstColumn) {
          onConfigurationSave(panelId, loadedConfiguration);
        }

        // Data loaded successfully - no need for message
      } else {
        message.error('Failed to load data');
      }
    } catch (error) {
      console.error('Error loading data:', error);
      message.error('Error loading data');
    } finally {
      setLoading(false);
    }
  };

  // Reusable function to call batch-comparison API with current configuration
  const callBatchComparisonAPI = async (xAxis: string, yAxis: string[], identifier: string, includeWindowConfig: boolean = false) => {
    if (!isDataLoaded || !configuration?.apiData?.selectedBatches) {
      return null;
    }

    const dateTimeRange = form.getFieldValue('dateTimeRange');
    if (!dateTimeRange || !dateTimeRange[0] || !dateTimeRange[1]) {
      return null;
    }

    const [startDateTime, endDateTime] = dateTimeRange;
    
    const systems = getSystemNames();
    const systemNames = systems.map((s: any) => s.systemName).join(' + ') || '';

    let apiPayload: any = {
      start_datetime: startDateTime.format('YYYY-MM-DD HH:mm:ss'),
      end_datetime: endDateTime.format('YYYY-MM-DD HH:mm:ss'),
      batch_ids: configuration.apiData.selectedBatches,
      x_axis: xAxis,
      y_axis: yAxis,
      identifier: identifier,
      compare_limit: configuration.apiData.compareLimit || 5,
      system: systemNames
    };

    // Add window config if it exists and is requested
    if (includeWindowConfig && configuration?.advanced?.windowConfig) {
      // Extract only the API-relevant fields from the window config
      const windowConfig = configuration.advanced.windowConfig;
      const apiWindowConfig = {
        target_batch_ids: windowConfig.target_batch_ids,
        pre_batch_ids: windowConfig.pre_batch_ids,
        post_batch_ids: windowConfig.post_batch_ids
      };
      
      apiPayload = {
        ...apiPayload,
        window_mode: true,
        window_config: apiWindowConfig
      };
    }

    console.log('API Call - Batch Comparison Payload:', {
      includeWindowConfig,
      hasWindowConfig: !!(configuration?.advanced?.windowConfig),
      apiPayload
    });

    try {
      const response = await postRequest('/file/explore-plc/batch-comparison', apiPayload);
      return response.data.data;
    } catch (error) {
      console.error('Error calling batch-comparison API:', error);
      message.error('Failed to update chart data');
      return null;
    }
  };

  // Helper function to call API with temporary configuration
  const callBatchComparisonAPIWithTempConfig = async (xAxis: string, yAxis: string[], identifier: string, tempConfig: any) => {
    if (!isDataLoaded || !tempConfig?.apiData?.selectedBatches) {
      return null;
    }

    const dateTimeRange = form.getFieldValue('dateTimeRange');
    if (!dateTimeRange || !dateTimeRange[0] || !dateTimeRange[1]) {
      return null;
    }

    const [startDateTime, endDateTime] = dateTimeRange;
    
    const systems = getSystemNames();
    const systemNames = systems.map((s: any) => s.systemName).join(' + ') || '';

    let apiPayload: any = {
      start_datetime: startDateTime.format('YYYY-MM-DD HH:mm:ss'),
      end_datetime: endDateTime.format('YYYY-MM-DD HH:mm:ss'),
      batch_ids: tempConfig.apiData.selectedBatches,
      x_axis: xAxis,
      y_axis: yAxis,
      identifier: identifier,
      compare_limit: tempConfig.apiData.compareLimit || 5,
      system: systemNames
    };

    // Add window config if it exists
    if (tempConfig?.advanced?.windowConfig) {
      const windowConfig = tempConfig.advanced.windowConfig;
      const apiWindowConfig = {
        target_batch_ids: windowConfig.target_batch_ids,
        pre_batch_ids: windowConfig.pre_batch_ids,
        post_batch_ids: windowConfig.post_batch_ids
      };
      
      apiPayload = {
        ...apiPayload,
        window_mode: true,
        window_config: apiWindowConfig
      };
    }

    console.log('API Call with Temp Config - Batch Comparison Payload:', {
      tempConfig: tempConfig?.advanced?.windowConfig,
      apiPayload
    });

    try {
      const response = await postRequest('/file/explore-plc/batch-comparison', apiPayload);
      return response.data.data;
    } catch (error) {
      console.error('Error calling batch-comparison API with temp config:', error);
      message.error('Failed to update chart data');
      return null;
    }
  };

  // Handle applying basic settings changes
  const handleApplyBasicSettings = async () => {
    if (!isDataLoaded || !panelId) {
      message.error('Please load data first');
      return;
    }

    const formValues = form.getFieldsValue();
    const { xAxisColumn, yAxis, group } = formValues;

    // Validate that required fields are selected
    if (!xAxisColumn) {
      message.error('Please select X-axis column');
      return;
    }

    if (!yAxis || yAxis.length === 0) {
      message.error('Please select at least one Y-axis column');
      return;
    }

    if (!group) {
      message.error('Please select identifier');
      return;
    }

    setBasicSettingsLoading(true);
    try {
      // Check current form state for window mode and build window config
      const formValues = form.getFieldsValue();
      const currentWindowMode = formValues.windowMode;
      
      let hasWindowConfig = false;
      let currentWindowConfig = null;
      
      if (currentWindowMode) {
        // Try to get window config from current form values
        const { target, pre, post } = formValues;
        
        if (target && target.length > 0) {
          // Build window config from current form values
          const { preBatchIds, postBatchIds } = convertPrePostToBatchIds(target, pre || 0, post || 0);
          
          currentWindowConfig = {
            target_batch_ids: target,
            pre_batch_ids: preBatchIds,
            post_batch_ids: postBatchIds
          };
          hasWindowConfig = true;
        } else if (configuration?.advanced?.windowConfig) {
          // Fall back to saved window config if form values incomplete
          currentWindowConfig = {
            target_batch_ids: configuration.advanced.windowConfig.target_batch_ids,
            pre_batch_ids: configuration.advanced.windowConfig.pre_batch_ids,
            post_batch_ids: configuration.advanced.windowConfig.post_batch_ids
          };
          hasWindowConfig = true;
        }
      }
      
      console.log('Apply Basic Settings - Window Config Check:', {
        currentWindowMode,
        formTarget: formValues.target,
        currentWindowConfig,
        hasWindowConfig
      });

      // Temporarily store window config for API call
      if (hasWindowConfig && currentWindowConfig) {
        const tempConfig = {
          ...configuration,
          advanced: {
            ...configuration?.advanced,
            windowMode: true,
            windowConfig: currentWindowConfig
          }
        };
        
        // Make API call with temporary config
        const apiData = await callBatchComparisonAPIWithTempConfig(xAxisColumn, yAxis, group, tempConfig);
        
        if (apiData) {
          const selectedColumns = {
            indices: yAxis.map((col: string) => availableColumns.indexOf(col)).filter((idx: number) => idx !== -1),
            headers: yAxis
          };

          const updatedConfiguration: PLCPanelConfiguration = {
            ...configuration,
            basic: {
              xAxisColumn: xAxisColumn,
              selectedColumns: selectedColumns,
              group: group,
            },
            title: `Batch Comparison - ${yAxis.join(', ')}`,
            lastModified: new Date().toISOString(),
            apiData: { ...configuration?.apiData, ...apiData },
          };

          onConfigurationSave(panelId, updatedConfiguration);
          message.success('Basic settings applied successfully');
        }
      } else {
        // Normal API call without window config
        const apiData = await callBatchComparisonAPI(xAxisColumn, yAxis, group, false);

        if (apiData) {
          const selectedColumns = {
            indices: yAxis.map((col: string) => availableColumns.indexOf(col)).filter((idx: number) => idx !== -1),
            headers: yAxis
          };

          const updatedConfiguration: PLCPanelConfiguration = {
            ...configuration,
            basic: {
              xAxisColumn: xAxisColumn,
              selectedColumns: selectedColumns,
              group: group,
            },
            title: `Batch Comparison - ${yAxis.join(', ')}`,
            lastModified: new Date().toISOString(),
            apiData: { ...configuration?.apiData, ...apiData },
          };

          onConfigurationSave(panelId, updatedConfiguration);
          message.success('Basic settings applied successfully');
        }
      }
    } catch (error) {
      console.error('Error applying basic settings:', error);
      message.error('Failed to apply basic settings');
    } finally {
      setBasicSettingsLoading(false);
    }
  };



  // Handle drawer close
  const handleClose = () => {
    // Don't reset fields or state - let them persist based on configuration
    // Only reset form if there's no configuration to restore
    if (!configuration) {
      form.resetFields();
      setSelectedGroup('BatchId');
      setWindowMode(false);
      setSelectedTargets([]);
      setAvailableBatches([]);
      setQualityBatchIds([]);
      setSelectedBatches([]);
      setIsDataLoaded(false);
    }
    onClose();
  };

  // Initialize form with configuration data
  useEffect(() => {
    if (open && configuration) {
      // Set state variables to match configuration
      setSelectedGroup(configuration.basic?.group || 'BatchId');
      setWindowMode(configuration.advanced?.windowMode || false);

      // Restore batch-related state from configuration
      if (configuration.apiData) {
        setIsDataLoaded(true);
        setSelectedBatches(configuration.apiData.selectedBatches || []);
        setCompareLimit(configuration.apiData.compareLimit || 5);
        
        // Restore availableBatches and qualityBatchIds from stored configuration
        setAvailableBatches(configuration.apiData.availableBatches || []);
        setQualityBatchIds(configuration.apiData.qualityBatchIds || []);
      } else {
        setIsDataLoaded(false);
        setSelectedBatches([]);
        setAvailableBatches([]);
        setQualityBatchIds([]);
        setCompareLimit(5);
      }

      // Set window configuration targets if they exist (support both old and new structures)
      if (configuration.advanced?.windowConfig?.target_batch_ids) {
        setSelectedTargets(configuration.advanced.windowConfig.target_batch_ids);
      } else if (configuration.advanced?.windowConfig?.target) {
        setSelectedTargets(configuration.advanced.windowConfig.target);
      }

      // Combine date and time for RangePicker
      const startDateTime = configuration.dataRange?.startDate && configuration.dataRange?.startTime
        ? dayjs(`${configuration.dataRange.startDate} ${configuration.dataRange.startTime}`)
        : null;
      const endDateTime = configuration.dataRange?.endDate && configuration.dataRange?.endTime
        ? dayjs(`${configuration.dataRange.endDate} ${configuration.dataRange.endTime}`)
        : null;

      form.setFieldsValue({
        dateTimeRange: startDateTime && endDateTime ? [startDateTime, endDateTime] : null,
        xAxisColumn: configuration.basic?.xAxisColumn || 'DateTime',
        yAxis: configuration.basic?.selectedColumns?.headers || [],
        group: configuration.basic?.group || 'BatchId',
        windowMode: configuration.advanced?.windowMode || false,
        target: configuration.advanced?.windowConfig?.target_batch_ids || configuration.advanced?.windowConfig?.target || [],
        pre: configuration.advanced?.windowConfig?.pre || undefined,
        post: configuration.advanced?.windowConfig?.post || undefined,
        selectedBatches: configuration.apiData?.selectedBatches || [],
      });
    } else if (open && !configuration) {
      // Only reset if opening with no configuration (new panel)
      setSelectedGroup('BatchId');
      setWindowMode(false);
      setSelectedTargets([]);
      setAvailableBatches([]);
      setQualityBatchIds([]);
      setSelectedBatches([]);
      setIsDataLoaded(false);
      setCompareLimit(5);

      // Set default date range (last 7 days)
      const defaultStartDateTime = dayjs().subtract(7, 'days').startOf('day');
      const defaultEndDateTime = dayjs().endOf('day');

      form.setFieldsValue({
        dateTimeRange: [defaultStartDateTime, defaultEndDateTime],
        xAxisColumn: 'DateTime',
        yAxis: [],
        group: 'BatchId',
        windowMode: false,
        selectedBatches: [],
      });
    }
  }, [open, configuration, form]);

  // Handle form submission (date range submission)
  const handleSubmit = async (values: any) => {
    if (!panelId) {
      return;
    }

    // Validate that date time range is provided
    if (!values.dateTimeRange || !values.dateTimeRange[0] || !values.dateTimeRange[1]) {
      message.error('Please select date and time range before submitting');
      return;
    }

    const [startDateTime, endDateTime] = values.dateTimeRange;

    // Validate that start is before end
    if (startDateTime.isAfter(endDateTime)) {
      message.error('Start date/time cannot be after end date/time');
      return;
    }

    setLoading(true);
    try {
      // Step 1: Fetch batch metadata first
      const success = await fetchBatchMetadata(startDateTime, endDateTime);

      if (!success) {
        // Keep drawer open on error, don't proceed
        setLoading(false);
        return;
      }

      // Save successful search to recent searches
      saveToRecentSearches(startDateTime, endDateTime);

      // Reset all other selections when date range changes
      setIsDataLoaded(false);
      setSelectedBatches([]);
      setWindowMode(false);
      setSelectedTargets([]);

      // Reset form fields for basic and advanced settings
      form.setFieldsValue({
        xAxisColumn: 'DateTime',
        yAxis: [],
        group: 'BatchId',
        windowMode: false,
        target: [],
        pre: undefined,
        post: undefined,
        selectedBatches: []
      });

      // Don't show success message - just proceed to batch selection
      // Don't save configuration yet - wait for second API call
      // Keep drawer open for batch selection - don't close

      // Don't show success message - just proceed
    } catch (error: any) {
      console.error('Error saving configuration:', error);
      message.error(error.message || 'Failed to load PLC data');
    } finally {
      setLoading(false);
    }
  };

  // Get available columns from API data - no defaults, everything dynamic
  const availableColumns = configuration?.apiData?.columns
    ? configuration.apiData.columns.filter((col: string) => col !== 'DateTime')
    : [];

  // X-axis dropdown should include DateTime + all available columns
  const allColumns = configuration?.apiData?.columns
    ? ['DateTime', ...configuration.apiData.columns]
    : ['DateTime'];
  
  // Identifier dropdown should include BatchId and all available columns (removed None)
  const groupingColumns = ['BatchId', ...availableColumns];

  // Helper function to convert pre/post numbers to actual batch IDs
  const convertPrePostToBatchIds = (targetBatches: string[], preCount: number, postCount: number) => {
    if (!availableBatches || availableBatches.length === 0) {
      return { preBatchIds: [], postBatchIds: [] };
    }

    // Find indices of target batches in availableBatches
    const targetIndices = targetBatches
      .map(batch => availableBatches.indexOf(batch))
      .filter(idx => idx !== -1)
      .sort((a, b) => a - b);

    if (targetIndices.length === 0) {
      return { preBatchIds: [], postBatchIds: [] };
    }

    const firstTargetIndex = targetIndices[0];
    const lastTargetIndex = targetIndices[targetIndices.length - 1];

    // Calculate pre batch IDs (before the first target)
    const preBatchIds: string[] = [];
    for (let i = Math.max(0, firstTargetIndex - preCount); i < firstTargetIndex; i++) {
      preBatchIds.push(availableBatches[i]);
    }

    // Calculate post batch IDs (after the last target)
    const postBatchIds: string[] = [];
    for (let i = lastTargetIndex + 1; i < Math.min(availableBatches.length, lastTargetIndex + 1 + postCount); i++) {
      postBatchIds.push(availableBatches[i]);
    }

    return { preBatchIds, postBatchIds };
  };

  // Render enhanced batch option with quality information
  const renderBatchOption = (batchInfo: { batch_id: string; batch_quality: number; is_batch_good: boolean }) => {
    const { batch_id, batch_quality, is_batch_good } = batchInfo;
    const qualityColor = is_batch_good ? '#52c41a' : '#ff4d4f';
    const qualityBg = is_batch_good ? '#f6ffed' : '#fff2f0';
    
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '4px 8px',
        backgroundColor: qualityBg,
        borderRadius: '4px',
        border: `1px solid ${qualityColor}20`
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '6px'
        }}>
          <span style={{ 
            fontWeight: 500,
            color: '#333'
          }}>
            {batch_id}
          </span>
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }}>
          <span style={{
            fontSize: '11px',
            color: qualityColor,
            fontWeight: 600,
            backgroundColor: qualityColor + '15',
            padding: '2px 6px',
            borderRadius: '10px'
          }}>
            {batch_quality.toFixed(2)}
          </span>
          <span style={{
            fontSize: '14px'
          }}>
            {is_batch_good ? '✅' : '❌'}
          </span>
        </div>
      </div>
    );
  };

  // Calculate pre/post options based on selected targets from availableBatches
  const calculatePrePostOptions = (targetValues: string[]): { preOptions: number[], postOptions: number[] } => {
    if (targetValues.length === 0 || !availableBatches || availableBatches.length === 0) {
      return { preOptions: [], postOptions: [] };
    }

    // Find the range of target indices in availableBatches
    const targetIndices = targetValues
      .map(val => availableBatches.indexOf(val))
      .filter(idx => idx !== -1)
      .sort((a, b) => a - b);
    
    if (targetIndices.length === 0) return { preOptions: [], postOptions: [] };

    const minTargetIndex = targetIndices[0];
    const maxTargetIndex = targetIndices[targetIndices.length - 1];

    // Calculate pre options (batches before the first target)
    const preCount = minTargetIndex;
    const preOptions = preCount > 0 ? Array.from({ length: preCount }, (_, i) => i + 1) : [];

    // Calculate post options (batches after the last target)
    const postCount = availableBatches.length - 1 - maxTargetIndex;
    const postOptions = postCount > 0 ? Array.from({ length: postCount }, (_, i) => i + 1) : [];

    return { preOptions, postOptions };
  };

  // Handle target selection change
  const handleTargetChange = (values: string[]) => {
    setSelectedTargets(values);
    form.setFieldValue('target', values);

    // Clear pre/post selections when targets change
    form.setFieldValue('pre', undefined);
    form.setFieldValue('post', undefined);
  };

  // Handle window mode submit
  const handleWindowSubmit = async () => {
    if (!panelId || !isDataLoaded) return;

    const formValues = form.getFieldsValue();
    const { target, pre, post } = formValues;

    if (!target || target.length === 0) {
      message.error('Please select at least one target value');
      return;
    }

    setWindowLoading(true);
    try {
      // Get current form values instead of configuration values for X/Y axis
      const formValues = form.getFieldsValue();
      const currentXAxis = formValues.xAxisColumn || 'DateTime';
      const currentYAxis = formValues.yAxis || [];
      const currentIdentifier = formValues.group || 'BatchId';

      console.log('Window Mode - Using Current Form Values:', {
        xAxis: currentXAxis,
        yAxis: currentYAxis,
        identifier: currentIdentifier,
        target,
        pre,
        post
      });

      // Convert pre/post numbers to actual batch IDs
      const { preBatchIds, postBatchIds } = convertPrePostToBatchIds(target, pre || 0, post || 0);

      // Create temporary window configuration for API call
      const tempWindowConfig = {
        target_batch_ids: target,
        pre_batch_ids: preBatchIds,
        post_batch_ids: postBatchIds
      };

      // Temporarily update configuration reference for API call
      const originalConfig = configuration;
      const tempConfiguration = {
        ...configuration,
        advanced: {
          ...configuration?.advanced,
          windowMode: true,
          windowConfig: tempWindowConfig,
        }
      };

      // Temporarily replace configuration for the API call
      (window as any).tempConfig = tempConfiguration;

      // Make direct API call with window configuration
      const dateTimeRange = form.getFieldValue('dateTimeRange');
      const [startDateTime, endDateTime] = dateTimeRange;
      
      const systems = getSystemNames();
      const systemNames = systems.map((s: any) => s.systemName).join(' + ') || '';

      const apiPayload = {
        start_datetime: startDateTime.format('YYYY-MM-DD HH:mm:ss'),
        end_datetime: endDateTime.format('YYYY-MM-DD HH:mm:ss'),
        batch_ids: configuration?.apiData?.selectedBatches || [],
        x_axis: currentXAxis,
        y_axis: currentYAxis,
        identifier: currentIdentifier,
        compare_limit: configuration?.apiData?.compareLimit || 5,
        window_mode: true,
        window_config: tempWindowConfig,
        system: systemNames
      };

      const response = await postRequest('/file/explore-plc/batch-comparison', apiPayload);
      const apiData = response.data?.data;

      if (apiData) {
        // Create final window mode configuration
        const windowConfig = {
          enabled: true,
          groupColumn: selectedGroup,
          target_batch_ids: target,
          pre_batch_ids: preBatchIds,
          post_batch_ids: postBatchIds
        };

        const updatedConfiguration: PLCPanelConfiguration = {
          ...configuration,
          advanced: {
            ...configuration?.advanced,
            windowMode: true,
            windowConfig: windowConfig,
          },
          lastModified: new Date().toISOString(),
          // Update apiData with new windowed data
          apiData: {
            ...configuration?.apiData,
            ...apiData,
            windowMode: true,
            windowConfig: windowConfig
          }
        };

        console.log('Window Mode - Saving Configuration:', {
          windowConfig,
          fullConfiguration: updatedConfiguration
        });

        onConfigurationSave(panelId, updatedConfiguration);
        message.success('Window configuration applied successfully');
      } else {
        message.error('Failed to apply window configuration');
      }
    } catch (error) {
      console.error('Error applying window configuration:', error);
      message.error('Failed to apply window configuration');
    } finally {
      setWindowLoading(false);
    }
  };

  // Handle window mode toggle
  const handleWindowModeChange = (e: any) => {
    const checked = e.target.checked;
    setWindowMode(checked);
    form.setFieldValue('windowMode', checked);
  };



  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '16px',
            fontWeight: 600
          }}>
            <SettingOutlined style={{ color: '#1890ff' }} />
            Panel Configuration
          </div>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={handleClose}
            size="small"
            disabled={isMasterLoading}
          />
        </div>
      }
      placement="right"
      width={450}
      open={open}
      onClose={handleClose}
      closable={false}
      maskClosable={!isMasterLoading}
      styles={{
        body: { padding: '0', position: 'relative' }
      }}
    >
      {/* Full Screen Loading Overlay */}
      <Spin 
        spinning={isMasterLoading} 
        tip={getLoadingMessage()}
        size="large"
        style={{
          minHeight: '100vh'
        }}
      >
        <div 
          className="plc-config-drawer" 
          style={{ 
            minHeight: '100vh',
            pointerEvents: isMasterLoading ? 'none' : 'auto'
          }}
          onKeyDown={(e) => {
            if (isMasterLoading) {
              e.preventDefault();
              e.stopPropagation();
            }
          }}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            className="plc-config-form"
          >
            {/* Data Selection Section */}
            <div style={{
              background: '#ffffff',
              borderRadius: '12px',
              padding: '20px',
              margin: '16px',
              marginBottom: '20px',
              border: '1px solid #e8e8e8',
              boxShadow: '0 2px 8px rgba(0,0,0,0.06)'
            }}>
              {/* Date and Time Range Selection */}
              <Form.Item
                label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Date & Time Range</span>}
                name="dateTimeRange"
                style={{ marginBottom: '16px' }}
                rules={[
                  { required: true, message: 'Please select date and time range!' }
                ]}
              >
                <RangePicker
                  showTime={{ format: 'HH:mm:ss' }}
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder={['Start Date & Time', 'End Date & Time']}
                  disabled={batchMetaLoading}
                  style={{ width: '100%', borderRadius: '8px' }}
                  size="middle"
                  presets={createDatePresets()}
                />
              </Form.Item>

              {/* Submit Button in Data Selection Zone */}
              <div style={{ marginTop: '20px' }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={batchMetaLoading}
                  disabled={batchMetaLoading}
                  style={{
                    width: '100%',
                    height: '40px',
                    fontSize: '14px',
                    fontWeight: 600,
                    borderRadius: '8px',
                    background: '#1890ff',
                    border: 'none'
                  }}
                  size="large"
                >
                  {batchMetaLoading ? 'Fetching Batches...' : 'Submit Date Range'}
                </Button>
              </div>

              {/* Batch Selection Section */}
              {availableBatches.length > 0 && (
                <div style={{
                  background: '#f0f8ff',
                  borderRadius: '8px',
                  padding: '16px',
                  border: '1px solid #91d5ff',
                  marginTop: '16px'
                }}>
                  <div style={{
                    marginBottom: '12px',
                    fontSize: '13px',
                    fontWeight: 500,
                    color: '#1890ff'
                  }}>
                    📊 Batch Selection (Max: {compareLimit})
                  </div>

                  <Form.Item
                    label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Select Batches</span>}
                    name="selectedBatches"
                    style={{ marginBottom: '12px' }}
                    rules={[
                      { required: true, message: 'Please select at least one batch!' },
                      {
                        validator: (_, value) => {
                          if (value && value.length > compareLimit) {
                            return Promise.reject(new Error(`Maximum ${compareLimit} batches allowed`));
                          }
                          return Promise.resolve();
                        }
                      }
                    ]}
                  >
                    <Select
                      mode="multiple"
                      placeholder={`Select up to ${compareLimit} batches`}
                      value={selectedBatches}
                      onChange={setSelectedBatches}
                      disabled={loading || batchMetaLoading}
                      size="middle"
                      style={{
                        borderRadius: '8px',
                        width: '100%'
                      }}
                      maxTagCount="responsive"
                      optionLabelProp="label"
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                      }
                    >
                      {qualityBatchIds.length > 0 ? (
                        qualityBatchIds.map(batchInfo => (
                          <Option 
                            key={batchInfo.batch_id} 
                            value={batchInfo.batch_id}
                            label={batchInfo.batch_id}
                          >
                            {renderBatchOption(batchInfo)}
                          </Option>
                        ))
                      ) : (
                        availableBatches.map(batch => (
                          <Option key={batch} value={batch} label={batch}>
                            {batch}
                          </Option>
                        ))
                      )}
                    </Select>
                  </Form.Item>

                  <Button
                    type="primary"
                    loading={loading && !batchMetaLoading}
                    disabled={selectedBatches.length === 0 || loading || batchMetaLoading}
                    size="middle"
                    style={{
                      borderRadius: '8px',
                      fontWeight: 500,
                      height: '36px',
                      background: '#52c41a',
                      borderColor: '#52c41a',
                      boxShadow: '0 2px 4px rgba(82, 196, 26, 0.2)'
                    }}
                    block
                    onClick={handleLoadData}
                  >
                    Load Data ({selectedBatches.length} batches selected)
                  </Button>

                  {isDataLoaded && (
                    <div style={{
                      textAlign: 'center',
                      marginTop: '8px',
                      fontSize: '12px',
                      color: '#52c41a',
                      fontWeight: 500
                    }}>
                      ✅ Data loaded - Configure axes below
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Basic Settings */}
            <div style={{
              background: '#ffffff',
              borderRadius: '12px',
              padding: '20px',
              margin: '0 16px 20px 16px',
              border: `1px solid ${isDataLoaded ? '#e8e8e8' : '#d9d9d9'}`,
              boxShadow: isDataLoaded ? '0 2px 8px rgba(0,0,0,0.06)' : 'none',
              opacity: isDataLoaded ? 1 : 0.5
            }}>
              <div style={{
                marginBottom: '20px',
                paddingBottom: '12px',
                borderBottom: `2px solid ${isDataLoaded ? '#1890ff' : '#d9d9d9'}`
              }}>
                <Title level={5} style={{
                  margin: 0,
                  color: isDataLoaded ? '#1890ff' : '#999',
                  fontSize: '16px',
                  fontWeight: 600,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  ⚙️ Basic Settings
                  {!isDataLoaded && <span style={{ fontSize: '12px', color: '#999', fontWeight: 400 }}>(Load data first)</span>}
                </Title>
              </div>
              <div>
                <Form.Item
                  label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>X Axis</span>}
                  name="xAxisColumn"
                  style={{ marginBottom: '16px' }}
                >
                  <Select
                    placeholder="Select X Axis Column"
                    disabled={!isDataLoaded || basicSettingsLoading}
                    size="middle"
                    style={{
                      borderRadius: '8px',
                      width: '100%'
                    }}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                  >
                    {allColumns.map(column => (
                      <Option key={column} value={column}>
                        {column}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Y Axis (Multi-select)</span>}
                  name="yAxis"
                  style={{ marginBottom: '16px' }}
                >
                  <Select
                    mode="multiple"
                    placeholder="Select columns for Y Axis"
                    allowClear
                    disabled={!isDataLoaded || basicSettingsLoading}
                    size="middle"
                    style={{
                      borderRadius: '8px',
                      width: '100%'
                    }}
                    maxTagCount="responsive"
                    showSearch
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                  >
                    {availableColumns.map(column => (
                      <Option key={column} value={column}>
                        {column}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  label={<span style={{ fontSize: '13px', fontWeight: 500, color: '#666' }}>Identifier</span>}
                  name="group"
                  style={{ marginBottom: '16px' }}
                >
                  <Select
                    placeholder="BatchId"
                    disabled={!isDataLoaded || basicSettingsLoading}
                    size="middle"
                    style={{
                      borderRadius: '8px',
                      width: '100%'
                    }}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase()?.includes(input.toLowerCase())
                    }
                  >
                    {groupingColumns.map(column => (
                      <Option key={column} value={column}>
                        {column}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                {/* Apply Basic Settings Button */}
                <div style={{ marginTop: '20px' }}>
                  <Button
                    type="primary"
                    loading={basicSettingsLoading}
                    disabled={!isDataLoaded || basicSettingsLoading}
                    onClick={handleApplyBasicSettings}
                    style={{
                      width: '100%',
                      height: '40px',
                      fontSize: '14px',
                      fontWeight: 600,
                      borderRadius: '8px',
                      background: '#52c41a',
                      borderColor: '#52c41a'
                    }}
                    size="large"
                  >
                    {basicSettingsLoading ? 'Applying Settings...' : 'Apply Basic Settings'}
                  </Button>
                </div>
              </div>
            </div>

            {/* Advanced Settings */}
            <div style={{
              background: '#ffffff',
              borderRadius: '12px',
              padding: '20px',
              margin: '0 16px 20px 16px',
              border: `1px solid ${isDataLoaded ? '#e8e8e8' : '#d9d9d9'}`,
              boxShadow: isDataLoaded ? '0 2px 8px rgba(0,0,0,0.06)' : 'none',
              opacity: isDataLoaded ? 1 : 0.5
            }}>
              <div style={{
                marginBottom: '20px',
                paddingBottom: '12px',
                borderBottom: `2px solid ${isDataLoaded ? '#fa8c16' : '#d9d9d9'}`
              }}>
                <Title level={5} style={{
                  margin: 0,
                  color: isDataLoaded ? '#fa8c16' : '#999',
                  fontSize: '16px',
                  fontWeight: 600,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  🔧 Advanced Settings
                  {!isDataLoaded && <span style={{ fontSize: '12px', color: '#999', fontWeight: 400 }}>(Load data first)</span>}
                </Title>
              </div>
              <div>
                <Form.Item
                  name="windowMode"
                  valuePropName="checked"
                  style={{ marginBottom: '16px' }}
                >
                  <Checkbox
                    disabled={!isDataLoaded || windowLoading}
                    onChange={handleWindowModeChange}
                    style={{
                      fontSize: '13px',
                      color: isDataLoaded && !windowLoading ? '#666' : '#999'
                    }}
                  >
                    Window Mode
                  </Checkbox>
                </Form.Item>

                {/* Window Mode Configuration */}
                {windowMode && (
                  <div style={{
                    background: '#f6ffed',
                    borderRadius: '8px',
                    padding: '16px',
                    border: '1px solid #b7eb8f',
                    marginBottom: '16px'
                  }}>
                    <div style={{
                      marginBottom: '12px',
                      fontSize: '13px',
                      fontWeight: 600,
                      color: '#52c41a'
                    }}>
                      🎯 Window Configuration
                    </div>

                    <Form.Item
                      label={<span style={{ fontSize: '12px', fontWeight: 500, color: '#666' }}>Target Batches (Multi-select)</span>}
                      name="target"
                      style={{ marginBottom: '12px' }}
                    >
                      <Select
                        mode="multiple"
                        placeholder="Select target batch IDs"
                        allowClear
                        disabled={windowLoading}
                        size="small"
                        style={{ borderRadius: '6px', width: '100%' }}
                        maxTagCount="responsive"
                        onChange={handleTargetChange}
                        optionLabelProp="label"
                        showSearch
                        filterOption={(input, option) =>
                          (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                        }
                      >
                        {qualityBatchIds.length > 0 ? (
                          qualityBatchIds.map(batchInfo => (
                            <Option 
                              key={batchInfo.batch_id} 
                              value={batchInfo.batch_id}
                              label={batchInfo.batch_id}
                            >
                              <div style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                padding: '2px 4px'
                              }}>
                                <div style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '4px'
                                }}>
                                  <span style={{ fontWeight: 500, fontSize: '12px' }}>
                                    {batchInfo.batch_id}
                                  </span>
                                </div>
                                <div style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '4px'
                                }}>
                                  <span style={{
                                    fontSize: '10px',
                                    color: batchInfo.is_batch_good ? '#52c41a' : '#ff4d4f',
                                    fontWeight: 600
                                  }}>
                                    {batchInfo.batch_quality.toFixed(1)}
                                  </span>
                                  <span style={{ fontSize: '12px' }}>
                                    {batchInfo.is_batch_good ? '✅' : '❌'}
                                  </span>
                                </div>
                              </div>
                            </Option>
                          ))
                        ) : (
                          availableBatches.map(batchId => (
                            <Option key={batchId} value={batchId} label={batchId}>
                              {batchId}
                            </Option>
                          ))
                        )}
                      </Select>
                    </Form.Item>

                    <div style={{ display: 'flex', gap: '8px' }}>
                      <Form.Item
                        label={<span style={{ fontSize: '12px', fontWeight: 500, color: '#666' }}>Pre</span>}
                        name="pre"
                        style={{ flex: 1, marginBottom: '8px' }}
                      >
                        <Select
                          placeholder="Select"
                          size="small"
                          style={{ borderRadius: '6px' }}
                          disabled={selectedTargets.length === 0 || windowLoading}
                        >
                          {calculatePrePostOptions(selectedTargets).preOptions.map(num => (
                            <Option key={num} value={num}>
                              {num} ({num === 1 ? 'immediate previous' : `previous ${num} combined`})
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>

                      <Form.Item
                        label={<span style={{ fontSize: '12px', fontWeight: 500, color: '#666' }}>Post</span>}
                        name="post"
                        style={{ flex: 1, marginBottom: '8px' }}
                      >
                        <Select
                          placeholder="Select"
                          size="small"
                          style={{ borderRadius: '6px' }}
                          disabled={selectedTargets.length === 0 || windowLoading}
                        >
                          {calculatePrePostOptions(selectedTargets).postOptions.map(num => (
                            <Option key={num} value={num}>
                              {num} ({num === 1 ? 'immediate next' : `next ${num} combined`})
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </div>

                    {/* Window Mode Submit Button */}
                    <Button
                      type="primary"
                      onClick={handleWindowSubmit}
                      loading={windowLoading}
                      disabled={windowLoading}
                      style={{
                        width: '100%',
                        height: '32px',
                        fontSize: '12px',
                        fontWeight: 600,
                        borderRadius: '6px',
                        background: '#52c41a',
                        border: 'none',
                        marginTop: '8px'
                      }}
                      size="small"
                    >
                      Apply Window Settings
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </Form>
        </div>
      </Spin>
    </Drawer>
  );
};

export default PLCPanelConfigDrawer;

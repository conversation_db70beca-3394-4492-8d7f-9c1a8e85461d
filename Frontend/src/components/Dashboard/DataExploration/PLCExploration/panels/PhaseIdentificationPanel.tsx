import React, { useState, useEffect, useRef } from 'react';
import { Button, Empty } from 'antd';
import { SettingOutlined, BranchesOutlined } from '@ant-design/icons';
import {
  PhaseIdentificationPanelProps
} from '../types/PLCTypes';

const PhaseIdentificationPanel: React.FC<PhaseIdentificationPanelProps> = ({
  configuration,
  isFullScreen = false,
  onOpenConfiguration
}) => {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  const isConfigured = configuration &&
                      configuration.dataRange?.startDate &&
                      configuration.dataRange?.endDate;

  // Monitor container size changes
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setContainerSize({ width: offsetWidth, height: offsetHeight });
      }
    };

    updateSize();
    
    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Handle configuration button click
  const handleConfigureClick = () => {
    if (onOpenConfiguration) {
      onOpenConfiguration();
    }
  };

  // Render empty state with configuration prompt when panel is not configured
  if (!configuration || !isConfigured) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full w-full"
        style={{ minHeight: '300px', width: '100%' }}
      >
        <Empty
          image={<BranchesOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-gray-700 mb-2">
                {!configuration ? 'Phase Identification Panel Not Configured' : 'Configuration Incomplete'}
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                {!configuration
                  ? 'Configure this panel to identify and analyze process phases in your PLC data'
                  : 'Please complete the configuration by selecting data sources and phase parameters'
                }
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
                style={{
                  background: '#52c41a',
                  borderColor: '#52c41a',
                  boxShadow: '0 2px 4px rgba(82, 196, 26, 0.2)'
                }}
              >
                Configure Panel
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  // For now, when configured, show a "coming soon" message since APIs are not integrated yet
  return (
    <div
      ref={containerRef}
      className="flex items-center justify-center h-full w-full"
      style={{ minHeight: '300px', width: '100%' }}
    >
      <Empty
        image={<BranchesOutlined style={{ fontSize: '48px', color: '#52c41a' }} />}
        description={
          <div className="text-center">
            <h3 className="text-base font-medium text-gray-800 mb-2">
              Phase Identification Ready
            </h3>
            <p className="text-gray-500 mb-4 text-sm">
              Panel is configured and ready. API integration coming soon for phase analysis functionality.
            </p>
            <Button
              type="primary"
              icon={<SettingOutlined />}
              onClick={handleConfigureClick}
              style={{
                background: '#52c41a',
                borderColor: '#52c41a',
                boxShadow: '0 2px 4px rgba(82, 196, 26, 0.2)'
              }}
            >
              Reconfigure Panel
            </Button>
          </div>
        }
      />
    </div>
  );
};

export default PhaseIdentificationPanel; 